<?php

namespace App\Model\Entity\EntityTrait;

use App\Enums\EntityFields\ERandselOrder;
use App\Enums\Options\ERandselOrderApprovalType;
use App\Utility\Encryptor;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;

trait RandselOrderTrait
{

    public function changeStatus($status, ERandselOrderApprovalType $randselOrderApprovalType): static
    {
//        debug($status);
        $this->status = $status;
        $this->approval_type = $randselOrderApprovalType->value;
        $this->status_modified = FrozenTime::now();
        return $this;
    }

    /**
     * @return array
     */
    public function responseData(): array
    {
        // $response = [];
        // $decrypted = $this->decryptDataArray();
        // foreach ($decrypted as $key => $value) {
        //     if (match ($key) {
        //         ERandselOrder::ID->value,
        //         ERandselOrder::MAKER_ID->value,
        //         ERandselOrder::MEMBER_ID->value,
        //         ERandselOrder::GENERAL_USER_ID->value,
        //         ERandselOrder::PRODUCT_ID->value,
        //         ERandselOrder::PRODUCT_NAME->value,
        //         ERandselOrder::PRICE->value,
        //         ERandselOrder::STATUS->value,
        //         ERandselOrder::STATUS_MODIFIED->value,
        //         ERandselOrder::APPROVAL_TYPE->value,
        //         ERandselOrder::IS_CONFIRMED->value,
        //         ERandselOrder::CONFIRMED->value,
        //         ERandselOrder::NAME1->value,
        //         ERandselOrder::NAME2->value,
        //         ERandselOrder::NAME1_HURIGANA->value,
        //         ERandselOrder::NAME2_HURIGANA->value,
        //         ERandselOrder::ZIP_CODE->value,
        //         ERandselOrder::TDFK_CD->value,
        //         ERandselOrder::ADDRESS1->value,
        //         ERandselOrder::ADDRESS2->value,
        //         ERandselOrder::ADDRESS3->value,
        //         ERandselOrder::TEL->value,
        //         ERandselOrder::EMAIL->value,
        //         ERandselOrder::EMAIL_SEND_NG_FLG->value,
        //         ERandselOrder::SURVEY_JSON->value,
        //         ERandselOrder::CREATED->value,
        //         ERandselOrder::MODIFIED->value,
        //         => true,
        //         default => false
        //     }) {
        //         $response[$key] = $value;
        //     }
        // }

        $response = $this->toArray();

        Log::debug("responseData: " . print_r($response, true));

        $response[ERandselOrder::NAME1->value] = $response['general_user']['user_profile']['decrypted_last_name'];
        $response[ERandselOrder::NAME2->value] = $response['general_user']['user_profile']['decrypted_first_name'];
        $response[ERandselOrder::NAME1_HURIGANA->value] = $response['general_user']['user_profile']['decrypted_last_name_kana'];
        $response[ERandselOrder::NAME2_HURIGANA->value] = $response['general_user']['user_profile']['decrypted_first_name_kana'];
        $response[ERandselOrder::ZIP_CODE->value] = $response['general_user']['user_profile']['decrypted_zip_code'];
        $response[ERandselOrder::TDFK_CD->value] = $response['general_user']['user_profile']['decrypted_prefecture_code'];
        $response[ERandselOrder::ADDRESS1->value] = $response['general_user']['user_profile']['decrypted_address1'];
        $response[ERandselOrder::ADDRESS2->value] = $response['general_user']['user_profile']['decrypted_address2'];
        $response[ERandselOrder::ADDRESS3->value] = $response['general_user']['user_profile']['decrypted_address3'];
        $response[ERandselOrder::TEL->value] = $response['general_user']['user_profile']['decrypted_tel'];
        $response[ERandselOrder::EMAIL->value] = $response->general_user->email;
        $response[ERandselOrder::EMAIL_SEND_NG_FLG->value] = $response->general_user->user_profile->email_send_ng_flg;
        return $response;
    }

    public function decryptDataArray(): array
    {
        $decrypted = [];
        foreach ($this->toArray() as $key => $value) {
            $decrypted[$key] = match ($key) {
//                ERandselOrder::ID->value,
//                ERandselOrder::MAKER_ID->value,
//                ERandselOrder::MEMBER_ID->value,
//                ERandselOrder::PRODUCT_ID->value,
//                ERandselOrder::PRODUCT_NAME->value,
//                ERandselOrder::PRICE->value,
//                ERandselOrder::STATUS->value,
//                ERandselOrder::STATUS_MODIFIED->value,
//                ERandselOrder::APPROVAL_TYPE->value,
//                ERandselOrder::IS_CONFIRMED->value,
//                ERandselOrder::CONFIRMED->value,
                ERandselOrder::NAME1->value,
                ERandselOrder::NAME2->value,
                ERandselOrder::NAME1_HURIGANA->value,
                ERandselOrder::NAME2_HURIGANA->value,
                ERandselOrder::ZIP_CODE->value,
                ERandselOrder::TDFK_CD->value,
                ERandselOrder::ADDRESS1->value,
                ERandselOrder::ADDRESS2->value,
                ERandselOrder::ADDRESS3->value,
                ERandselOrder::TEL->value,
                ERandselOrder::EMAIL->value,
//                ERandselOrder::EMAIL_SEND_NG_FLG->value,
                ERandselOrder::SURVEY_JSON->value,
//                ERandselOrder::CREATED->value,
//                ERandselOrder::MODIFIED->value,
                => $value ? Encryptor::decrypt($value) : null,
                default => $value,
            };
        }
        return $decrypted;
    }

    static public function dataEncrypt(array $rawData): array
    {
        $encrypted = [];
        foreach ($rawData as $key => $value) {
            $encrypted[$key] = match ($key) {
//                ERandselOrder::ID->value,
//                ERandselOrder::MAKER_ID->value,
//                ERandselOrder::MEMBER_ID->value,
//                ERandselOrder::PRODUCT_ID->value,
//                ERandselOrder::PRODUCT_NAME->value,
//                ERandselOrder::PRICE->value,
//                ERandselOrder::STATUS->value,
//                ERandselOrder::STATUS_MODIFIED->value,
//                ERandselOrder::APPROVAL_TYPE->value,
//                ERandselOrder::IS_CONFIRMED->value,
//                ERandselOrder::CONFIRMED->value,
                ERandselOrder::NAME1->value,
                ERandselOrder::NAME2->value,
                ERandselOrder::NAME1_HURIGANA->value,
                ERandselOrder::NAME2_HURIGANA->value,
                ERandselOrder::ZIP_CODE->value,
                ERandselOrder::TDFK_CD->value,
                ERandselOrder::ADDRESS1->value,
                ERandselOrder::ADDRESS2->value,
                ERandselOrder::ADDRESS3->value,
                ERandselOrder::TEL->value,
                ERandselOrder::EMAIL->value,
//                ERandselOrder::EMAIL_SEND_NG_FLG->value,
                ERandselOrder::SURVEY_JSON->value,
//                ERandselOrder::CREATED->value,
//                ERandselOrder::MODIFIED->value,
                => Encryptor::encrypt($value),
                default => $value,
            };
        }
        return $encrypted;
    }
}
